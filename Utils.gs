function getSheetByName(sheetName) {
  const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
  return ss.getSheetByName(sheetName);
}

function getSheetData(sheetName) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) return null;
  
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();
  const headers = values[0];
  
  return {
    headers: headers,
    data: values.slice(1)
  };
}

function formatDate(date) {
  return Utilities.formatDate(date, Session.getScriptTimeZone(), "dd.MM.yyyy");
}

function formatTime(time) {
  return Utilities.formatDate(time, Session.getScriptTimeZone(), "HH:mm");
} 