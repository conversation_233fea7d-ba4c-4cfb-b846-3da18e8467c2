// Hilfsfunktion zum Setzen der Metadaten für ein Sheet
function setSheetMetadata(sheet, teamleiter, unterueberschrift) {
  // Lösche alte Metadaten falls vorhanden
  const oldMetadata = sheet.getRange('A1:B2');
  if (oldMetadata) {
    oldMetadata.clearContent();
  }
  
  // Setze neue Metadaten
  sheet.getRange('A1:B2').setValues([
    ['Teamleiter', teamleiter],
    ['Unterüberschrift', unterueberschrift]
  ]);
  
  // Formatiere Metadaten
  sheet.getRange('A1:B2')
    .setBackground('#f8f9fa')
    .setFontWeight('bold')
    .setHorizontalAlignment('left')
    .setVerticalAlignment('middle')
    .setWrap(true);
}

function setupSheets() {
  const ss = SpreadsheetApp.openById('1I7LUVV8Nl9HhgZ6pJ-YAiuhVIWVvnkRMUvW_yEWGQCg');
  
  // Hilfsfunktion zum Erstellen oder Aktualisieren eines Sheets
  function getOrCreateSheet(sheetName) {
    let sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      sheet = ss.insertSheet(sheetName);
    }
    return sheet;
  }
  
  // Pommes Schichten
  let sheet = getOrCreateSheet('PommesSchichten');
  setSheetMetadata(sheet, 'Toni', 'Schichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Bar Schichten
  sheet = getOrCreateSheet('BarSchichten');
  setSheetMetadata(sheet, 'Andrea', 'Je 3 Personen pro Schicht - eine Person für Bedienung der Kaffeemaschine!\n1 Person im Bereitschaftsdienst');
  sheet.getRange('A4:F4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2', 'Person 3', 'Bereitschaft']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Spül Schichten
  sheet = getOrCreateSheet('SpuelSchichten');
  setSheetMetadata(sheet, 'Paul', 'Paul organisiert Schichten mit Spülboy-Gruppe selbstständig.\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Freitag'],
    ['Freitag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['11:30-14:00'],
    ['16:00-20:00'],
    ['09:30-13:30'],
    ['13:30-17:30'],
    ['09:30-13:30'],
    ['13:30-17:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Kindertöpfern
  sheet = getOrCreateSheet('Kindertoepfern');
  setSheetMetadata(sheet, '', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Lebendwerkstatt
  sheet = getOrCreateSheet('Lebendwerkstatt');
  setSheetMetadata(sheet, '', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Siebdruck
  sheet = getOrCreateSheet('Siebdruck');
  setSheetMetadata(sheet, '', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Ausstellungsaufsicht
  sheet = getOrCreateSheet('Ausstellungsaufsicht');
  setSheetMetadata(sheet, '', 'Merchstand und/oder Ausstellungsraumaufsicht - wird demnächst noch geklärt.\nSchichten: (je 1 Person pro Schicht)');
  sheet.getRange('A4:C4').setValues([['Tag', 'Zeit', 'Person']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Kuchenspenden
  sheet = getOrCreateSheet('Kuchenspenden');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:C4').setValues([['Name', 'Tag', 'Info (optional)']]);
  sheet.setFrozenRows(4);
  
  // Plakate
  sheet = getOrCreateSheet('Plakate');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:B4').setValues([['Ort', 'Person']]);
  sheet.setFrozenRows(4);
  
  // Aufbauhelfer
  sheet = getOrCreateSheet('Aufbauhelfer');
  setSheetMetadata(sheet, '', '');
  sheet.getRange('A4:B4').setValues([['Name', 'Tag']]);
  sheet.setFrozenRows(4);
  
  // Formatiere alle Sheets
  const sheets = ss.getSheets();
  sheets.forEach(sheet => {
    const lastColumn = sheet.getLastColumn();
    if (lastColumn > 0) {
      // Setze Spaltenbreiten
      sheet.setColumnWidths(1, lastColumn, 150);
      
      // Formatiere Header
      const headerRange = sheet.getRange(4, 1, 1, lastColumn);
      headerRange.setBackground('#2c3e50')
                 .setFontColor('white')
                 .setFontWeight('bold');
      
      // Formatiere Zellen
      const lastRow = sheet.getLastRow();
      if (lastRow > 4) {
        const dataRange = sheet.getRange(5, 1, lastRow - 4, lastColumn);
        dataRange.setHorizontalAlignment('left')
                 .setVerticalAlignment('middle')
                 .setWrap(true);
      }
    }
  });
} 