<style>
  body {
    background-color: #f8f9fa;
  }
  
  .navbar {
    margin-bottom: 20px;
  }
  
  .list-links {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }
  
  .list-links a {
    color: #0d6efd;
    text-decoration: none;
    transition: color 0.2s ease;
  }
  
  .list-links a:hover {
    color: #0a58ca;
    text-decoration: underline;
  }
  
  .separator {
    color: #6c757d;
  }
  
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
  }
  
  .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  
  .btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
  }
  
  .table-responsive {
    margin-top: 20px;
  }
  
  @media (max-width: 768px) {
    .container {
      padding: 10px;
    }
  }
</style> 