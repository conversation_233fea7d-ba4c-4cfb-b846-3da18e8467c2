<script>
// Allgemeine Hilfsfunktionen
function showLoading() {
  document.getElementById('content').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Laden...</span></div></div>';
}

function showError(message) {
  console.error('Fehler:', message);
  document.getElementById('content').innerHTML = `
    <div class="alert alert-danger" role="alert">
      ${message}
    </div>
  `;
}

function showSuccess(message) {
  document.getElementById('content').innerHTML = `
    <div class="alert alert-success" role="alert">
      ${message}
    </div>
  `;
}

function loadBarSchichten() {
  console.log('Lade Bar-Schichten...');
  showLoading();
  
  // Zuerst das Template laden
  google.script.run
    .withSuccessHandler(function(templateHtml) {
      console.log('Template geladen:', templateHtml);
      if (!templateHtml) {
        console.error('Kein Template erhalten');
        showError('<PERSON><PERSON> beim Laden des Templates');
        return;
      }
      document.getElementById('content').innerHTML = templateHtml;
      
      // Dann die Daten laden
      google.script.run
        .withSuccessHandler(function(data) {
          console.log('Daten geladen:', data);
          if (!data) {
            console.error('Keine Daten erhalten');
            showError('Keine Daten erhalten');
            return;
          }
          displayBarSchichten(data);
        })
        .withFailureHandler(function(error) {
          console.error('Fehler beim Laden der Daten:', error);
          showError('Fehler beim Laden der Daten: ' + error);
        })
        .getBarSchichten();
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Laden des Templates:', error);
      showError('Fehler beim Laden des Templates: ' + error);
    })
    .include('BarSchichten2');
}

function displayBarSchichten(data) {
  console.log('Display Bar-Schichten:', data);
  if (!data) {
    showError('Fehler beim Laden der Bar-Schichten');
    return;
  }

  const tableBody = document.getElementById('barTableBody');
  if (!tableBody) {
    console.error('barTableBody nicht gefunden');
    return;
  }
  
  tableBody.innerHTML = '';

  data.data.forEach((row, rowIndex) => {
    const tr = document.createElement('tr');
    
    // Tag (nicht editierbar)
    const tdTag = document.createElement('td');
    tdTag.textContent = row[0];
    tr.appendChild(tdTag);
    
    // Zeit (nicht editierbar)
    const tdZeit = document.createElement('td');
    tdZeit.textContent = row[1];
    tr.appendChild(tdZeit);
    
    // Person 1 (editierbar)
    const tdPerson1 = document.createElement('td');
    const inputPerson1 = document.createElement('input');
    inputPerson1.type = 'text';
    inputPerson1.className = 'form-control';
    inputPerson1.value = row[2] || '';
    inputPerson1.placeholder = 'Name eingeben';
    inputPerson1.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 2, this.value);
    });
    tdPerson1.appendChild(inputPerson1);
    tr.appendChild(tdPerson1);
    
    // Person 2 (editierbar)
    const tdPerson2 = document.createElement('td');
    const inputPerson2 = document.createElement('input');
    inputPerson2.type = 'text';
    inputPerson2.className = 'form-control';
    inputPerson2.value = row[3] || '';
    inputPerson2.placeholder = 'Name eingeben';
    inputPerson2.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 3, this.value);
    });
    tdPerson2.appendChild(inputPerson2);
    tr.appendChild(tdPerson2);
    
    // Person 3 (editierbar)
    const tdPerson3 = document.createElement('td');
    const inputPerson3 = document.createElement('input');
    inputPerson3.type = 'text';
    inputPerson3.className = 'form-control';
    inputPerson3.value = row[4] || '';
    inputPerson3.placeholder = 'Name eingeben';
    inputPerson3.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 4, this.value);
    });
    tdPerson3.appendChild(inputPerson3);
    tr.appendChild(tdPerson3);
    
    // Bereitschaft (editierbar)
    const tdBereitschaft = document.createElement('td');
    const inputBereitschaft = document.createElement('input');
    inputBereitschaft.type = 'text';
    inputBereitschaft.className = 'form-control';
    inputBereitschaft.value = row[5] || '';
    inputBereitschaft.placeholder = 'Name eingeben';
    inputBereitschaft.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 5, this.value);
    });
    tdBereitschaft.appendChild(inputBereitschaft);
    tr.appendChild(tdBereitschaft);
    
    tableBody.appendChild(tr);
  });
}

function updateBarSchicht(rowIndex, columnIndex, value) {
  google.script.run
    .withSuccessHandler(function(success) {
      if (!success) {
        showError('Fehler beim Speichern der Änderung');
      }
    })
    .withFailureHandler(function(error) {
      showError('Fehler beim Speichern der Änderung: ' + error);
    })
    .updateBarSchicht(rowIndex, columnIndex, value);
}

</script> 