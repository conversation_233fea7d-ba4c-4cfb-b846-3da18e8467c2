<script>
// Allgemeine Hilfsfunktionen
function showLoading() {
  document.getElementById('content').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Laden...</span></div></div>';
}

function showError(message) {
  console.error('Fehler:', message);
  document.getElementById('content').innerHTML = `
    <div class="alert alert-danger" role="alert">
      ${message}
    </div>
  `;
}

function showSuccess(message) {
  document.getElementById('content').innerHTML = `
    <div class="alert alert-success" role="alert">
      ${message}
    </div>
  `;
}

// Generische Loader-Funktion für alle Listen
function loadList(listName, templateName, getDataFunction, displayFunction) {
  console.log(`Lade ${listName}...`);
  showLoading();

  // Zuerst das Template laden
  google.script.run
    .withSuccessHandler(function(templateHtml) {
      console.log('Template geladen:', templateName);
      if (!templateHtml) {
        console.error('Kein Template erhalten');
        showError('Fehler beim Laden des Templates');
        return;
      }
      document.getElementById('content').innerHTML = templateHtml;

      // Dann die Daten laden
      google.script.run
        .withSuccessHandler(function(data) {
          console.log('Daten geladen:', data);
          if (!data) {
            console.error('Keine Daten erhalten');
            showError('Keine Daten erhalten');
            return;
          }
          // Rufe die spezifische Display-Funktion auf
          window[displayFunction](data);
        })
        .withFailureHandler(function(error) {
          console.error('Fehler beim Laden der Daten:', error);
          showError('Fehler beim Laden der Daten: ' + error);
        })
        [getDataFunction]();
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Laden des Templates:', error);
      showError('Fehler beim Laden des Templates: ' + error);
    })
    .include(templateName);
}

// Spezifische Loader-Funktionen
function loadBarSchichten() {
  loadList('Bar-Schichten', 'BarSchichten2', 'getBarSchichten', 'displayBarSchichten');
}

// Bar-spezifische Funktionen sind jetzt in BarSchichten2.html

</script> 