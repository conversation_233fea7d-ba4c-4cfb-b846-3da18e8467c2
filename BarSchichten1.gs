function getBarSchichten() {
  console.log('Starte getBarSchichten()');
  const sheet = getSheetByName(CONFIG.SHEETS.BAR);
  if (!sheet) {
    console.error('Sheet nicht gefunden:', CONFIG.SHEETS.BAR);
    return null;
  }
  
  // Hole Metadaten
  const metadata = sheet.getRange('A1:B2').getValues();
  const teamleiter = metadata[0][1];
  const unterueberschrift = metadata[1][1];
  
  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();
  console.log('Gefundene Werte:', values);
  
  const headers = values[3]; // Header ist jetzt in Zeile 4
  const data = values.slice(4); // Daten beginnen ab Zeile 5
  
  const result = {
    headers: headers,
    data: data,
    sheetName: CONFIG.SHEETS.BAR,
    teamleiter: teamleiter,
    unterueberschrift: unterueberschrift
  };
  
  console.log('Rückgabe:', result);
  return result;
}

function updateBarSchicht(rowIndex, columnIndex, value) {
  console.log('Starte updateBarSchicht:', {rowIndex, columnIndex, value});
  const sheet = getSheetByName(CONFIG.SHEETS.BAR);
  if (!sheet) {
    console.error('Sheet nicht gefunden:', CONFIG.SHEETS.BAR);
    return false;
  }
  
  try {
    // +5 weil: +1 für 0-basierter Index, +4 für Header-Zeile und Metadaten
    sheet.getRange(rowIndex + 5, columnIndex + 1).setValue(value);
    console.log('Schicht erfolgreich aktualisiert');
    return true;
  } catch (error) {
    console.error('Fehler beim Aktualisieren der Bar-Schicht:', error);
    return false;
  }
} 