function doGet() {
  return HtmlService.createTemplateFromFile('index')
    .evaluate()
    .setTitle('ListenMarkt')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

function include(filename) {
  try {
    console.log('Lade Template:', filename);
    const content = HtmlService.createHtmlOutputFromFile(filename).getContent();
    console.log('Template geladen:', filename);
    return content;
  } catch (error) {
    console.error('<PERSON><PERSON> beim <PERSON>den des Templates:', filename, error);
    return null;
  }
}

function getSpreadsheetId() {
  return '1I7LUVV8Nl9HhgZ6pJ-YAiuhVIWVvnkRMUvW_yEWGQCg';
}

function getMainPage() {
  return HtmlService.createTemplateFromFile('index')
    .evaluate()
    .getContent();
} 