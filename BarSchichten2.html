<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2>Bar Schichten</h2>
      <p class="text-muted" id="teamleiter"></p>
      <p class="text-muted" id="unterueberschrift"></p>
    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>Tag</th>
              <th>Zeit</th>
              <th>Person 1</th>
              <th>Person 2</th>
              <th>Person 3</th>
              <th>Bereitschaft</th>
            </tr>
          </thead>
          <tbody id="barTableBody">
            <!-- Wird dynamisch gefüllt -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
function displayBarSchichten(data) {
  console.log('Display Bar-Schichten:', data);
  if (!data) {
    showError('Fehler beim Laden der Bar-Schichten');
    return;
  }

  // Aktualisiere Metadaten
  document.getElementById('teamleiter').textContent = 'Teamleiterin: ' + (data.teamleiter || '');
  document.getElementById('unterueberschrift').innerHTML = data.unterueberschrift || '';

  const tableBody = document.getElementById('barTableBody');
  if (!tableBody) {
    console.error('barTableBody nicht gefunden');
    return;
  }
  
  tableBody.innerHTML = '';

  data.data.forEach((row, rowIndex) => {
    const tr = document.createElement('tr');
    
    // Tag (nicht editierbar)
    const tdTag = document.createElement('td');
    tdTag.textContent = row[0];
    tr.appendChild(tdTag);
    
    // Zeit (nicht editierbar)
    const tdZeit = document.createElement('td');
    tdZeit.textContent = row[1];
    tr.appendChild(tdZeit);
    
    // Person 1 (editierbar)
    const tdPerson1 = document.createElement('td');
    const inputPerson1 = document.createElement('input');
    inputPerson1.type = 'text';
    inputPerson1.className = 'form-control';
    inputPerson1.value = row[2] || '';
    inputPerson1.placeholder = 'Name eingeben';
    inputPerson1.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 2, this.value);
    });
    tdPerson1.appendChild(inputPerson1);
    tr.appendChild(tdPerson1);
    
    // Person 2 (editierbar)
    const tdPerson2 = document.createElement('td');
    const inputPerson2 = document.createElement('input');
    inputPerson2.type = 'text';
    inputPerson2.className = 'form-control';
    inputPerson2.value = row[3] || '';
    inputPerson2.placeholder = 'Name eingeben';
    inputPerson2.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 3, this.value);
    });
    tdPerson2.appendChild(inputPerson2);
    tr.appendChild(tdPerson2);
    
    // Person 3 (editierbar)
    const tdPerson3 = document.createElement('td');
    const inputPerson3 = document.createElement('input');
    inputPerson3.type = 'text';
    inputPerson3.className = 'form-control';
    inputPerson3.value = row[4] || '';
    inputPerson3.placeholder = 'Name eingeben';
    inputPerson3.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 4, this.value);
    });
    tdPerson3.appendChild(inputPerson3);
    tr.appendChild(tdPerson3);
    
    // Bereitschaft (editierbar)
    const tdBereitschaft = document.createElement('td');
    const inputBereitschaft = document.createElement('input');
    inputBereitschaft.type = 'text';
    inputBereitschaft.className = 'form-control';
    inputBereitschaft.value = row[5] || '';
    inputBereitschaft.placeholder = 'Name eingeben';
    inputBereitschaft.addEventListener('change', function() {
      updateBarSchicht(rowIndex, 5, this.value);
    });
    tdBereitschaft.appendChild(inputBereitschaft);
    tr.appendChild(tdBereitschaft);
    
    tableBody.appendChild(tr);
  });
}

function updateBarSchicht(rowIndex, columnIndex, value) {
  google.script.run
    .withSuccessHandler(function(success) {
      if (!success) {
        showError('Fehler beim Speichern der Änderung');
      }
    })
    .withFailureHandler(function(error) {
      showError('Fehler beim Speichern der Änderung: ' + error);
    })
    .updateBarSchicht(rowIndex, columnIndex, value);
}
</script> 